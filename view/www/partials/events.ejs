<div class="calendar hevent" id="events-calendar">
    <div class="event-title">
      活动日历
    </div>
    <table width="100%" border="0" cellpadding="0" cellspacing="0" class="listTable">
      <tbody>
        <% events.forEach(function(event) { %>
          <% if (event.html==0) { %>
            <tr style="height: 50px;" class="event-row" data-event-id="<%= event.id %>" data-event-image="<%= event.image || '' %>" data-event-url="<%= event.url1 || '' %>">
              <td style="width: 50px;">
                <%- newFlag(event.new_flag_date)%><span>
                    <%= event.event_geo.name %>
                  </span>
                  <p
                    style="white-space: nowrap; margin: 5px 0px 0px; line-height: 10px; height: 10px; overflow: hidden;">
                    <%= formatDateTime(event.event_begin_date, event.event_end_date) %>
                  </p>
              </td>
              <td><span></span><i></i><a href="<%= event.url1 %>" target="_blank">
                  <%= event.subject %>
                </a></td>
            </tr>
            <% } else { %>
              <tr class="event-row" data-event-id="<%= event.id %>" data-event-image="<%= event.image || '' %>" data-event-url="<%= event.url1 || '' %>">
                <%= event.subject %>
              </tr>
              <% } %>
                <% }); %>
      </tbody>
    </table>
  </div>

<!-- 悬停显示的大图容器 -->
<div id="event-hover-image" class="event-hover-image">
  <a href="" target="_blank">
    <img src="" width="600" height="300" alt="活动图片" />
  </a>
</div>

<style>
.event-hover-image {
  position: fixed;
  z-index: 10000;
  display: none;
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 10px;
}

/* 左侧箭头（当弹出框在右侧时） */
.event-hover-image.show-right::before {
  content: '';
  position: absolute;
  left: -10px;
  top: var(--arrow-top, 30px);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 10px 10px 0;
  border-color: transparent #ddd transparent transparent;
}

.event-hover-image.show-right::after {
  content: '';
  position: absolute;
  left: -8px;
  top: var(--arrow-top, 30px);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 10px 10px 0;
  border-color: transparent white transparent transparent;
}

/* 右侧箭头（当弹出框在左侧时） */
.event-hover-image.show-left::before {
  content: '';
  position: absolute;
  right: -10px;
  top: var(--arrow-top, 30px);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 0 10px 10px;
  border-color: transparent transparent transparent #ddd;
}

.event-hover-image.show-left::after {
  content: '';
  position: absolute;
  right: -8px;
  top: var(--arrow-top, 30px);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 0 10px 10px;
  border-color: transparent transparent transparent white;
}

.event-hover-image img {
  display: block;
  border-radius: 4px;
}

.event-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.event-row:hover {
  background-color: #f5f5f5;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const eventRows = document.querySelectorAll('.event-row');
  const hoverImage = document.getElementById('event-hover-image');
  const hoverImageImg = hoverImage ? hoverImage.querySelector('img') : null;
  const hoverImageLink = hoverImage ? hoverImage.querySelector('a') : null;
  const eventsCalendar = document.getElementById('events-calendar');
  
  let hideTimeout = null;
  
  function showHoverImage(row) {
    // 清除任何pending的隐藏定时器
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      hideTimeout = null;
    }

    const eventImage = row.getAttribute('data-event-image');
    const eventUrl = row.getAttribute('data-event-url');

    if (eventImage && eventImage.trim() !== '') {
      hoverImageImg.src = eventImage;
      hoverImageLink.href = eventUrl || '#';

      const rowRect = row.getBoundingClientRect();
      const calendarRect = eventsCalendar.getBoundingClientRect();
      const windowWidth = window.innerWidth;

      // 判断列表位置：如果列表中心点在屏幕右半部分，说明列表在右侧
      const calendarCenter = calendarRect.left + (calendarRect.width / 2);
      const isListOnRight = calendarCenter > windowWidth / 2;

      // 获取当前行在事件列表中的索引
      const allRows = Array.from(eventRows);
      const rowIndex = allRows.indexOf(row);

      // 检查页面是否发生了滚动（通过检查页面滚动位置）
      const hasScrolled = window.pageYOffset > 0 || document.documentElement.scrollTop > 0;

      let imageLeft, imageTop;

      if (isListOnRight) {
        // 列表在右侧，弹出框显示在左侧
        imageLeft = calendarRect.left - 640;
        hoverImage.className = 'event-hover-image show-left';
      } else {
        // 列表在左侧，弹出框显示在右侧
        imageLeft = calendarRect.right + 20;
        hoverImage.className = 'event-hover-image show-right';
      }

      // 特殊逻辑：前5个项目且页面未滚动时，与carousel-wrapper保持重合
      if (rowIndex < 5 && !hasScrolled) {
        const carouselWrapper = document.querySelector('.carousel-wrapper');
        if (carouselWrapper) {
          const carouselRect = carouselWrapper.getBoundingClientRect();

          if (isListOnRight) {
            // 列表在右侧，弹出框显示在左侧，与carousel-wrapper右边缘对齐
            imageLeft = carouselRect.right - 620; // 弹出框宽度620px，使其右边缘与carousel-wrapper对齐
          } else {
            // 列表在左侧，弹出框显示在右侧，与carousel-wrapper左边缘对齐
            imageLeft = carouselRect.left - 12;
          }

          // 垂直位置与carousel-wrapper顶部对齐
          imageTop = carouselRect.top - 12;

          // 计算箭头位置：指向当前悬停的行，由于弹出框往上移动了10px，箭头位置需要相应调整
          const rowCenterRelativeToImage = rowRect.top + (rowRect.height / 2) - imageTop;
          hoverImage.style.setProperty('--arrow-top', rowCenterRelativeToImage + 'px');
        } else {
          // 如果找不到carousel-wrapper，使用默认逻辑
          imageTop = rowRect.top + (rowRect.height / 2) - 110;
          const rowCenterRelativeToImage = rowRect.top + (rowRect.height / 2) - imageTop - 20;
          hoverImage.style.setProperty('--arrow-top', rowCenterRelativeToImage + 'px');
        }
      } else {
        // 默认逻辑：相对于悬停的行定位
        imageTop = rowRect.top + (rowRect.height / 2) - 110;

        // 计算箭头应该指向的位置（行的中心向上偏移20px）
        const rowCenterRelativeToImage = rowRect.top + (rowRect.height / 2) - imageTop - 20;
        hoverImage.style.setProperty('--arrow-top', rowCenterRelativeToImage + 'px');
      }

      hoverImage.style.left = imageLeft + 'px';
      hoverImage.style.top = imageTop + 'px';
      hoverImage.style.display = 'block';
    }
  }
  
  function hideHoverImage() {
    // 使用短暂延时，给用户时间从行移动到弹出框
    hideTimeout = setTimeout(function() {
      hoverImage.style.display = 'none';
    }, 100);
  }
  
  if (hoverImage && hoverImageImg && hoverImageLink && eventsCalendar) {
    // 为每个活动行添加事件监听
    eventRows.forEach(function(row) {
      row.addEventListener('mouseenter', function() {
        showHoverImage(row);
      });
      
      row.addEventListener('mouseleave', function() {
        hideHoverImage();
      });
    });
    
    // 为弹出框本身添加事件监听，让鼠标可以移动到弹出框上
    hoverImage.addEventListener('mouseenter', function() {
      // 当鼠标进入弹出框时，取消隐藏
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        hideTimeout = null;
      }
    });
    
    hoverImage.addEventListener('mouseleave', function() {
      // 当鼠标离开弹出框时，立即隐藏
      hoverImage.style.display = 'none';
    });
  }
});
</script>
